#!/usr/bin/env python3
"""
Създава тестови PNG изображения за LVGL Image Button demo
Използва PIL (Pillow) за генериране на прости бутони
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    import os
except ImportError:
    print("Моля инсталирайте Pillow: pip install Pillow")
    exit(1)

def create_button_image(filename, size=(80, 80), color=(100, 150, 200), pressed=False):
    """Създава PNG изображение на бутон"""
    
    # Създаваме изображение с RGBA формат
    img = Image.new('RGBA', size, (0, 0, 0, 0))  # Прозрачен фон
    draw = ImageDraw.Draw(img)
    
    # Цветове за бутона
    if pressed:
        # По-тъмен цвят за натиснат бутон
        button_color = tuple(max(0, c - 50) for c in color)
        border_color = (50, 50, 50, 255)
    else:
        # Нормален цвят за освободен бутон
        button_color = color + (255,)  # Добавяме alpha канал
        border_color = (200, 200, 200, 255)
    
    # Рисуваме заоблен правоъгълник за бутона
    margin = 5
    draw.rounded_rectangle(
        [margin, margin, size[0] - margin, size[1] - margin],
        radius=10,
        fill=button_color,
        outline=border_color,
        width=2
    )
    
    # Добавяме текст
    try:
        # Опитваме се да използваме системен шрифт
        font = ImageFont.truetype("arial.ttf", 12)
    except:
        # Ако няма системен шрифт, използваме default
        font = ImageFont.load_default()
    
    text = "PRESS" if not pressed else "CLICK"
    
    # Центрираме текста
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    text_x = (size[0] - text_width) // 2
    text_y = (size[1] - text_height) // 2
    
    # Рисуваме текста
    text_color = (255, 255, 255, 255) if not pressed else (200, 200, 200, 255)
    draw.text((text_x, text_y), text, fill=text_color, font=font)
    
    # Запазваме изображението
    img.save(filename, 'PNG')
    print(f"Създадено: {filename} ({size[0]}x{size[1]})")

def main():
    """Създава тестовите изображения"""
    print("Създаване на тестови PNG изображения за LVGL Image Button...")
    
    # Създаваме изображения за бутоните
    create_button_image('btn_rel.png', size=(80, 80), color=(70, 130, 180), pressed=False)
    create_button_image('btn_pr.png', size=(80, 80), color=(70, 130, 180), pressed=True)
    
    # Проверяваме размерите на файловете
    for filename in ['btn_rel.png', 'btn_pr.png']:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            print(f"{filename}: {size} bytes")
    
    print("\nГотово! Файловете са готови за копиране на VK-RA6M5 board.")
    print("Копирайте btn_rel.png и btn_pr.png в root директорията на board-а.")

if __name__ == "__main__":
    main()
