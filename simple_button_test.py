# simple_button_test.py – Прост LVGL Button тест за VK-RA6M5
# Тестваме основните LVGL функции преди Image Button

print("=== Прост LVGL Button тест ===")

import gc, time
import lvgl as lv
from os import uname
from machine import SPI
from st77xx import St7789 as STdrv

# ── Debug функции за памет ──────────────────────────────────────────────────
def print_memory_info(label=""):
    """Показва информация за паметта"""
    gc.collect()
    free = gc.mem_free()
    alloc = gc.mem_alloc()
    total = free + alloc
    print(f"[MEM {label}] Free: {free:,} bytes, Alloc: {alloc:,} bytes, Total: {total:,} bytes")
    return free, alloc

def print_detailed_memory():
    """Показва детайлна информация за паметта"""
    print("=== Детайлна памет информация ===")
    try:
        # Опитваме се да получим информация за heap-овете
        import micropython
        micropython.mem_info()
    except:
        pass

    free, alloc = print_memory_info("CURRENT")
    print(f"Memory usage: {(alloc/(free+alloc)*100):.1f}%")
    print("=" * 40)

# ── Инициализация на LVGL и дисплея ───────────────────────────────────────────
print_memory_info("START")

lv.init()
print_memory_info("AFTER lv.init()")

gc.collect()
print_memory_info("AFTER gc.collect()")

if "VK-RA6M5" in uname().machine:
    print("Инициализиране на ST7789 дисплей...")
    lcd = STdrv(
        rot=0, res=(240, 240), spi=SPI(0, baudrate=50_000_000, polarity=1),
        cs="D6", dc="D8", bl="D7", rst="D9",
        doublebuffer=False, factor=60
    )
    print_memory_info("AFTER ST7789 init")

    lcd.set_backlight(100)
    print("ST7789 дисплей инициализиран")
    print_memory_info("AFTER backlight")

# ── Създаване на екран и бутон ──────────────────────────────────────────────
print("Създаване на LVGL екран...")
scr = lv.obj()
print_memory_info("AFTER scr create")

lv.scr_load(scr)
print_memory_info("AFTER scr_load")

# Създаваме обикновен бутон
print("Създаване на бутон...")
btn = lv.btn(scr)
print_memory_info("AFTER btn create")

btn.set_size(120, 50)
btn.center()
print_memory_info("AFTER btn setup")

# Добавяме текст
print("Добавяне на label...")
label = lv.label(btn)
print_memory_info("AFTER label create")

label.set_text("CLICK ME")
label.center()
print_memory_info("AFTER label setup")

# Event handler
click_count = 0
def btn_event_cb(event):
    global click_count
    if event.get_code() == lv.EVENT.CLICKED:
        click_count += 1
        print(f"Button clicked {click_count} times!")
        label.set_text(f"Clicks: {click_count}")
        print_memory_info(f"CLICK_{click_count}")

print("Добавяне на event handler...")
# Опитваме различни API варианти за event callbacks
try:
    btn.add_event_cb(btn_event_cb, lv.EVENT.ALL)
    print("Event callback добавен успешно")
except AttributeError:
    try:
        btn.add_event_cb(btn_event_cb, lv.EVENT.ALL, None)
        print("Event callback добавен с 3 параметъра")
    except:
        try:
            btn.set_event_cb(btn_event_cb)
            print("Event callback добавен с set_event_cb")
        except:
            print("Не мога да добавя event callback - ще работи без click detection")

print_memory_info("AFTER event_cb")

print("Бутон създаден. Натиснете го за тест...")
print_detailed_memory()

# ── Event Loop ─────────────────────────────────────────────────────────────
print("Стартиране на event loop...")
last = time.ticks_ms()
loop_count = 0

try:
    while True:
        lv.timer_handler()
        time.sleep_ms(5)
        loop_count += 1

        if time.ticks_diff(time.ticks_ms(), last) > 5000:
            gc.collect()
            print_memory_info(f"LOOP_{loop_count//1000}k")
            last = time.ticks_ms()

except KeyboardInterrupt:
    print("Тест спряно")
    print_detailed_memory()
